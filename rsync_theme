#!/bin/sh

##
## Deploy script for bitbucket pipelines
##

application=""
themes=""
username=""
ip=""

while [ "$#" -ne 0 ]; do
  case "$1" in
    --application)
      application="$2"
      shift 2
      ;;
    --theme)
      theme_value="$2"
      case "$theme_value" in
        all)
          themes="./*"
          ;;
        *)
          themes="$themes $theme_value"
          ;;
      esac
      shift 2
      ;;
    --username)
      username="$2"
      shift 2
      ;;
    --ip)
      ip="$2"
      shift 2
      ;;
    --purge-cache)
      purge_cache="$2"
      shift 2
      ;;
    *)
      echo "Error: Unknown option $1"
      exit 1
      ;;
  esac
done

if [ -z "$application" ] || [ -z "$themes" ] || [ -z "$username" ] || [ -z "$ip" ]; then
  echo "Error: Missing required parameters"
  exit 1
fi

# Run rsync command once
rsync -e "ssh -i $BITBUCKET_SSH_KEY_FILE -o StrictHostKeyChecking=no" -azP --omit-dir-times --no-perms --exclude={'vendor/','app/','bitbucket-pipelines.yml','composer.json','composer.lock','package.json','package.lock'} --delete $themes $username@$ip:/home/<USER>/applications/$application/public_html/wp-content/themes

apk add curl jq

API_ENDPOINT="https://api.cloudways.com/api/v1"
BEARER_TOKEN=$(curl -s -X POST -d "email=$CW_EMAIL" -d "api_key=$CW_API_KEY" "$API_ENDPOINT/oauth/access_token" | jq -r .access_token)
servers_info=$(curl -s -X GET -H "Authorization: Bearer $BEARER_TOKEN" "$API_ENDPOINT/server")

if [ "$(echo "$servers_info" | jq -r '.status')" = "true" ]; then
  echo "##### RESETTING FILE PERMISSIONS ON CLOUDWAYS #####"
  server_id=$(echo "$servers_info" | jq -r '.servers[] | select(.public_ip == "'$ip'") | .id')
  app_id=$(echo "$servers_info" | jq -r '.servers[] | select(.public_ip == "'$ip'") | .apps[] | select(.sys_user == "'$application'") | .id')

  curl -s -X POST -H "Authorization: Bearer $BEARER_TOKEN" \
       -d "server_id=$server_id" -d "app_id=$app_id" -d "ownership=sys_user" \
       "$API_ENDPOINT/app/manage/reset_permissions"
  echo "✅ File permissions were successfully reset on Cloudways."

  if [ "$purge_cache" = "true" ]; then
    cw_subdomain=$(echo "$servers_info" | jq -r '.servers[] | select(.public_ip == "'$ip'") | .apps[] | select(.sys_user == "'$application'") | .cname')
    cf_domain=$(echo "$cw_subdomain" | rev | cut -d. -f1,2 | rev)

    # **Skip cache purge for root domains**
    if [ "$cw_subdomain" = "$cf_domain" ]; then
      echo "⚠️ Skipping Cloudflare cache purge for root domain ($cf_domain)"
    else
      echo "##### PURGING CLOUDFLARE CACHE for ${cw_subdomain} #####"

      cf_zone_id=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones?name=${cf_domain}" \
       -H "Authorization: Bearer ${CF_API_TOKEN}" \
       -H "Content-Type: application/json" | jq -r '.result[0].id')

      curl -X POST "https://api.cloudflare.com/client/v4/zones/${cf_zone_id}/purge_cache" \
        -H "Authorization: Bearer ${CF_API_TOKEN}" \
        -H "Content-Type: application/json" \
        --data '{
          "hosts": ["'"${cw_subdomain}"'"]
        }'
      echo "✅ CloudFlare cache was successfully purged for ${cw_subdomain}"
    fi
  else
    echo "⚠️ Skipping Cloudflare cache purge for root domain ($cf_domain)"
  fi

else
  echo "❌ Error: Unable to retrieve server information"
fi

